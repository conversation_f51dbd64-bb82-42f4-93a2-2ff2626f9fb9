<template>
  <div class="app-container">
    <el-card>
      <!-- 搜索表单 -->
      <el-form ref="queryForm" :model="queryParams" inline size="mini">
        <el-form-item label="计划类型">
          <el-select v-model="queryParams.plan_type" placeholder="请选择计划类型" clearable>
            <el-option label="定向计划" :value="1"></el-option>
            <el-option label="公开计划" :value="2"></el-option>
            <el-option label="机构定向计划" :value="3"></el-option>
            <el-option label="机构普通计划" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="搜索关键词">
          <el-input v-model="queryParams.keyword" placeholder="请输入商品名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="店铺APPID">
          <el-input
            v-model="queryParams.shop_appid"
            placeholder="请输入店铺APPID"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="handleSearch">
            搜索
          </el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="handleReset"> 重置 </el-button>
        </el-form-item>
      </el-form>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 类别导航栏 -->
        <div class="category-nav">
          <div class="category-title">商品分类</div>
          <div class="category-tabs">
            <el-button
              :type="selectedCategoryId === '' ? 'primary' : 'default'"
              size="small"
              @click="handleCategorySelect('')"
              class="category-btn"
            >
              全部
            </el-button>
            <el-button
              v-for="category in categoryList"
              :key="category.value"
              :type="selectedCategoryId === category.value ? 'primary' : 'default'"
              size="small"
              @click="handleCategorySelect(category.value)"
              class="category-btn"
            >
              {{ category.label }}
            </el-button>
          </div>
        </div>

        <!-- 商品列表 -->
        <div v-loading="loading" class="product-list">
          <div v-if="productList.length === 0 && !loading" class="empty-state">
            <el-empty description="暂无商品数据"></el-empty>
          </div>
          <div v-else class="product-grid">
            <div
              v-for="product in productList"
              :key="product.product_id || Math.random()"
              class="product-card"
              @click="handleProductDetail(product)"
            >
              <!-- 商品图片轮播 -->
              <div class="product-image">
                <el-carousel
                  v-if="getProductImages(product).length > 1"
                  height="200px"
                  :autoplay="false"
                  indicator-position="outside"
                >
                  <el-carousel-item
                    v-for="(image, index) in getProductImages(product)"
                    :key="index"
                  >
                    <img :src="image" :alt="getProductTitle(product)" @error="handleImageError" />
                  </el-carousel-item>
                </el-carousel>
                <img
                  v-else
                  :src="getProductImage(product)"
                  :alt="getProductTitle(product)"
                  @error="handleImageError"
                />
              </div>

              <!-- 商品信息 -->
              <div class="product-info">
                <h3 class="product-title">{{ getProductTitle(product) }}</h3>
                <p class="product-subtitle" v-if="getProductSubTitle(product)">
                  {{ getProductSubTitle(product) }}
                </p>

                <!-- 价格信息 -->
                <div class="price-info">
                  <span class="current-price">¥{{ formatPrice(getMinPrice(product)) }}</span>
                </div>

                <!-- 佣金信息 -->
                <div class="commission-info" v-if="getCommissionRatio(product) > 0">
                  <el-tag type="success" size="mini">
                    佣金: {{ getCommissionRatio(product) }}%
                  </el-tag>
                  <el-tag type="warning" size="mini" style="margin-left: 5px">
                    ¥{{ getCommissionAmount(product) }}
                  </el-tag>
                </div>

                <!-- 商品ID信息 -->
                <div class="product-id-info">
                  <span class="product-id">商品ID: {{ getProductId(product) }}</span>
                </div>

                <!-- 店铺信息 -->
                <div class="shop-info" v-if="getShopInfo(product)">
                  <span class="shop-name">商家店铺ID: {{ getShopInfo(product) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-wrapper" v-if="productList.length > 0">
            <el-button
              v-if="hasMore"
              type="primary"
              :loading="loading"
              @click="loadMore"
              style="width: 30%; margin-top: 20px"
            >
              加载更多
            </el-button>
            <div v-else class="no-more-data">
              <span>已加载全部数据</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getList } from '@/api/product/wxlproduct'
import { getPlatformCategoryOptions } from '@/utils/options'

export default {
  name: 'WXLProductList',
  data() {
    return {
      loading: false,
      productList: [],
      categoryList: [],
      selectedCategoryId: '',
      queryParams: {
        plan_type: 2,
        keyword: '',
        shop_appid: '',
        page_size: 10,
        next_key: '',
      },
      hasMore: true,
    }
  },
  async mounted() {
    await this.getCategoryList()
    this.getProductList()
  },
  methods: {
    async getProductList(isLoadMore = false) {
      if (this.loading) return

      this.loading = true
      try {
        const params = { ...this.queryParams }
        if (this.selectedCategoryId) {
          params.category = {
            category_id: this.selectedCategoryId,
          }
        }
        const response = await getList(params)

        if (response.code === 0 && response.data) {
          if (isLoadMore) {
            this.productList = [...this.productList, ...response.data]
          } else {
            this.productList = response.data
          }
          this.hasMore = response.data.length >= this.queryParams.page_size
          this.queryParams.next_key = response.data[response.data.length - 1].next_key
        } else {
          this.$message.error(response.msg || '获取商品列表失败')
        }
      } catch (error) {
        console.error('获取商品列表失败:', error)
        this.$message.error('获取商品列表失败')
      } finally {
        this.loading = false
      }
    },

    handleSearch() {
      this.queryParams.next_key = ''
      this.hasMore = true
      this.getProductList()
    },

    handleReset() {
      this.queryParams = {
        plan_type: 2,
        keyword: '',
        shop_appid: '',
        page_size: 10,
        next_key: '',
      }
      this.selectedCategoryId = ''
      this.hasMore = true
      this.getProductList()
    },

    loadMore() {
      this.getProductList(true)
    },

    handleProductDetail(product) {
      const productId = this.getProductId(product)
      const shopAppid = this.getShopInfo(product)
      const planType = this.queryParams.plan_type

      if (!productId || !shopAppid || !planType) {
        this.$message.error('缺少必要参数，无法查看详情')
        return
      }

      this.$router.push({
        path: '/product/wxlproduct/detail',
        query: {
          id: productId,
          shopAppid: shopAppid,
          planType: planType,
        },
      })
    },

    getProductImage(product) {
      const images =
        (product &&
          product.product &&
          product.product.productInfo &&
          product.product.productInfo.headImgs) ||
        []
      return images.length > 0 ? images[0].trim() : '/static/images/no-image.png'
    },

    // 获取所有商品图片
    getProductImages(product) {
      const images =
        (product &&
          product.product &&
          product.product.productInfo &&
          product.product.productInfo.headImgs) ||
        []
      return images.length > 0 ? images : ['/static/images/no-image.png']
    },

    // 获取商品ID
    getProductId(product) {
      return (product && product.product && product.product.productId) || ''
    },

    getProductTitle(product) {
      return (
        (product &&
          product.product &&
          product.product.productInfo &&
          product.product.productInfo.title) ||
        '暂无标题'
      )
    },

    getProductSubTitle(product) {
      return (
        (product &&
          product.product &&
          product.product.productInfo &&
          product.product.productInfo.subTitle) ||
        ''
      )
    },

    getMinPrice(product) {
      const skus =
        (product &&
          product.product &&
          product.product.productInfo &&
          product.product.productInfo.skus) ||
        []
      if (skus.length === 0) return 0
      return Math.min(...skus.map((sku) => sku.salePrice || 0))
    },

    getCommissionRatio(product) {
      const commissionInfo = product && product.product && product.product.commissionInfo
      if (commissionInfo && commissionInfo.serviceRatio) {
        return (commissionInfo.serviceRatio / 10000).toFixed(2)
      }
      return '0.00'
    },

    getCommissionAmount(product) {
      const minPrice = this.formatPrice(this.getMinPrice(product))
      const commissionRatio = this.getCommissionRatio(product)
      if (minPrice > 0 && commissionRatio > 0) {
        return ((minPrice * commissionRatio) / 100).toFixed(2)
      }
      return '0.00'
    },

    getShopInfo(product) {
      return (product && product.product && product.product.shopAppid) || ''
    },

    formatPrice(price) {
      if (!price || isNaN(price)) return '0.00'
      return (price / 100).toFixed(2)
    },

    handleImageError(event) {
      event.target.src = '/static/images/no-image.png'
    },

    async getCategoryList() {
      try {
        const categories = await getPlatformCategoryOptions({ level: 1 })
        console.log(categories)
        this.categoryList = categories || []
      } catch (error) {
        console.error('获取类别列表失败:', error)
        this.$message.error('获取类别列表失败')
      }
    },

    handleCategorySelect(categoryId) {
      this.selectedCategoryId = categoryId
      this.queryParams.next_key = ''
      this.hasMore = true
      this.getProductList()
    },
  },
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.product-list {
  flex: 1;
  min-width: 0;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.product-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  background: #fff;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.product-image {
  height: 200px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.product-info {
  padding: 15px;
}

.product-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0 0 10px 0;
}

.price-info {
  margin-bottom: 10px;

  .current-price {
    font-size: 18px;
    font-weight: 600;
    color: #e6a23c;
  }
}

.commission-info {
  margin-bottom: 8px;
}

.product-id-info {
  margin-bottom: 8px;

  .product-id {
    font-size: 12px;
    color: #606266;
    font-weight: 500;
  }
}

.shop-info {
  .shop-name {
    font-size: 12px;
    color: #909399;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.main-content {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.category-nav {
  width: 200px;
  flex-shrink: 0;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  height: fit-content;
  border: 1px solid #e4e7ed;

  .category-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;
  }

  .category-tabs {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .category-btn {
    width: 100%;
    justify-content: flex-start;
    text-align: left;
  }
}

.pagination-wrapper {
  text-align: center;
  margin-top: 20px;

  .no-more-data {
    color: #909399;
    font-size: 14px;
    padding: 20px 0;
  }
}
</style>
