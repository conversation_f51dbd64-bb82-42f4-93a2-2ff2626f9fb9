<template>
  <div class="product-detail-page">
    <div class="page-header">
      <el-button @click="goBack" icon="el-icon-arrow-left" type="primary">返回</el-button>
      <h1>商品详情</h1>
    </div>

    <div v-if="loading" class="loading-container">
      <el-loading text="加载中..."></el-loading>
    </div>

    <div v-else-if="productDetail" class="detail-container">
      <!-- 商品基本信息 -->
      <el-card class="info-card" shadow="hover">
        <div slot="header" class="card-header">
          <span class="card-title">基本信息</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="product-images">
              <h3>商品图片</h3>
              <el-carousel height="400px" :autoplay="false" indicator-position="outside">
                <el-carousel-item v-for="(image, index) in getHeadImages()" :key="index">
                  <img
                    :src="image"
                    :alt="productDetail.title"
                    class="carousel-image"
                    @error="handleImageError"
                  />
                </el-carousel-item>
              </el-carousel>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="basic-info">
              <h2 class="product-title">{{ productDetail.title }}</h2>
              <p v-if="productDetail.subTitle" class="product-subtitle">
                {{ productDetail.subTitle }}
              </p>

              <div class="info-grid">
                <div class="info-item">
                  <span class="label">商品ID:</span>
                  <span class="value">{{ productDetail.productId }}</span>
                </div>
                <div class="info-item">
                  <span class="label">店铺APPID:</span>
                  <span class="value">{{ productDetail.shopAppid }}</span>
                </div>
                <div class="info-item">
                  <span class="label">商品状态:</span>
                  <el-tag :type="getStatusType(productDetail.status)">{{
                    getStatusText(productDetail.status)
                  }}</el-tag>
                </div>
                <div class="info-item">
                  <span class="label">创建时间:</span>
                  <span class="value">{{ formatTime(productDetail.createTime) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">更新时间:</span>
                  <span class="value">{{ formatTime(productDetail.updateTime) }}</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- SKU信息 -->
      <el-card
        class="info-card"
        shadow="hover"
        v-if="productDetail.skus && productDetail.skus.length > 0"
      >
        <div slot="header" class="card-header">
          <span class="card-title">SKU规格信息</span>
        </div>
        <el-table :data="productDetail.skus" border stripe>
          <el-table-column prop="skuId" label="SKU ID" width="150"></el-table-column>
          <el-table-column label="规格属性" min-width="200">
            <template slot-scope="scope">
              <div v-if="scope.row.skuAttrs" class="sku-attrs">
                <el-tag
                  v-for="attr in scope.row.skuAttrs"
                  :key="attr.attrKey"
                  size="mini"
                  class="attr-tag"
                >
                  {{ attr.attrKey }}: {{ attr.attrValue }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="售价" width="120">
            <template slot-scope="scope">
              <span class="price">¥{{ formatPrice(scope.row.salePrice) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="stockNum" label="库存" width="100"></el-table-column>
          <el-table-column label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="mini">
                {{ scope.row.status === 1 ? '正常' : '下架' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 商品分类 -->
      <el-card
        class="info-card"
        shadow="hover"
        v-if="productDetail.catsV2 && productDetail.catsV2.length > 0"
      >
        <div slot="header" class="card-header">
          <span class="card-title">商品分类</span>
        </div>
        <div class="category-list">
          <div v-for="cat in productDetail.catsV2" :key="cat.catId" class="category-item">
            <el-tag type="info" size="medium">
              {{ cat.catName || `分类ID: ${cat.catId}` }}
            </el-tag>
            <span v-if="cat.level" class="category-level">等级: {{ cat.level }}</span>
          </div>
        </div>
      </el-card>

      <!-- 佣金信息 -->
      <el-card class="info-card" shadow="hover" v-if="productDetail.commissionInfo">
        <div slot="header" class="card-header">
          <span class="card-title">佣金信息</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="commission-item">
              <span class="label">佣金比例:</span>
              <span class="value commission-rate"
                >{{ formatCommissionRatio(productDetail.commissionInfo.serviceRatio) }}%</span
              >
            </div>
          </el-col>
          <el-col :span="8">
            <div class="commission-item">
              <span class="label">佣金类型:</span>
              <span class="value">{{ getCommissionType(productDetail.commissionInfo.type) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="commission-item">
              <span class="label">佣金状态:</span>
              <el-tag
                :type="productDetail.commissionInfo.status === 1 ? 'success' : 'danger'"
                size="mini"
              >
                {{ productDetail.commissionInfo.status === 1 ? '有效' : '无效' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 发布优惠券 -->
      <el-card
        class="info-card"
        shadow="hover"
        v-if="productDetail.publishCoupons && productDetail.publishCoupons.length > 0"
      >
        <div slot="header" class="card-header">
          <span class="card-title">发布优惠券</span>
        </div>
        <el-table :data="productDetail.publishCoupons" border stripe>
          <el-table-column prop="couponId" label="优惠券ID" width="150"></el-table-column>
          <el-table-column prop="couponName" label="优惠券名称" min-width="200"></el-table-column>
          <el-table-column label="优惠金额" width="120">
            <template slot-scope="scope">
              <span class="coupon-amount">¥{{ formatPrice(scope.row.discountAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="minAmount" label="最低消费" width="120">
            <template slot-scope="scope"> ¥{{ formatPrice(scope.row.minAmount) }} </template>
          </el-table-column>
          <el-table-column label="有效期" width="200">
            <template slot-scope="scope">
              {{ formatTime(scope.row.startTime) }} ~ {{ formatTime(scope.row.endTime) }}
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="getCouponStatusType(scope.row.status)" size="mini">
                {{ getCouponStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 合作优惠券 -->
      <el-card
        class="info-card"
        shadow="hover"
        v-if="productDetail.cooperativeCoupons && productDetail.cooperativeCoupons.length > 0"
      >
        <div slot="header" class="card-header">
          <span class="card-title">合作优惠券</span>
        </div>
        <el-table :data="productDetail.cooperativeCoupons" border stripe>
          <el-table-column prop="couponId" label="优惠券ID" width="150"></el-table-column>
          <el-table-column prop="couponName" label="优惠券名称" min-width="200"></el-table-column>
          <el-table-column label="优惠金额" width="120">
            <template slot-scope="scope">
              <span class="coupon-amount">¥{{ formatPrice(scope.row.discountAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="partnerName" label="合作方" width="150"></el-table-column>
          <el-table-column label="合作类型" width="120">
            <template slot-scope="scope">
              <el-tag type="warning" size="mini">{{
                scope.row.cooperationType || '普通合作'
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="getCouponStatusType(scope.row.status)" size="mini">
                {{ getCouponStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 商品描述信息 -->
      <el-card class="info-card" shadow="hover" v-if="productDetail.descInfo">
        <div slot="header" class="card-header">
          <span class="card-title">商品描述</span>
        </div>
        <div class="desc-content">
          <div v-if="productDetail.descInfo.desc" class="desc-text">
            <h4>文字描述</h4>
            <p>{{ productDetail.descInfo.desc }}</p>
          </div>
          <div v-if="getDescImages().length > 0" class="desc-images">
            <h4>描述图片</h4>
            <div class="desc-image-grid">
              <div v-for="(image, index) in getDescImages()" :key="index" class="desc-image-item">
                <img
                  :src="image"
                  :alt="'描述图片' + (index + 1)"
                  @error="handleImageError"
                  @click="previewImage(image)"
                />
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <div v-else class="no-data">
      <el-empty description="暂无商品数据"></el-empty>
    </div>

    <!-- 图片预览 -->
    <el-dialog :visible.sync="imagePreviewVisible" width="80%" center>
      <img :src="previewImageUrl" style="width: 100%" />
    </el-dialog>
  </div>
</template>

<script>
import { getDetail } from '@/api/product/wxlproduct'

export default {
  name: 'ProductDetail',
  data() {
    return {
      loading: false,
      productDetail: null,
      imagePreviewVisible: false,
      previewImageUrl: '',
    }
  },
  created() {
    this.getProductDetail()
  },
  methods: {
    async getProductDetail() {
      const productId = this.$route.params.id || this.$route.query.id
      const shopAppid = this.$route.params.shopAppid || this.$route.query.shopAppid
      const planType = this.$route.params.planType || this.$route.query.planType

      if (!productId) {
        this.$message.error('缺少商品ID参数')
        this.goBack()
        return
      }

      if (!shopAppid) {
        this.$message.error('缺少店铺APPID参数')
        this.goBack()
        return
      }

      if (!planType) {
        this.$message.error('缺少计划类型参数')
        this.goBack()
        return
      }

      this.loading = true
      try {
        const response = await getDetail({
          productId: productId,
          shopAppid: shopAppid,
          planType: planType,
        })
        if (response.code === 0 && response.data) {
          const productData = response.data
          this.productDetail = {
            ...productData.product.productInfo,
            shopAppid: productData.product.shopAppid,
            productId: productData.product.productId,
            commissionInfo: productData.product.commissionInfo,
            publishCoupons: productData.publishCoupons || [],
            cooperativeCoupons: productData.cooperativeCoupons || [],
          }
        } else {
          this.$message.error(response.msg || '获取商品详情失败')
        }
      } catch (error) {
        console.error('获取商品详情失败:', error)
        this.$message.error('获取商品详情失败')
      } finally {
        this.loading = false
      }
    },

    goBack() {
      this.$router.go(-1)
    },

    getHeadImages() {
      if (
        this.productDetail &&
        this.productDetail.headImgs &&
        this.productDetail.headImgs.length > 0
      ) {
        return this.productDetail.headImgs
      }
      return ['https://via.placeholder.com/400x400?text=No+Image']
    },

    getDescImages() {
      if (this.productDetail && this.productDetail.descInfo && this.productDetail.descInfo.imgs) {
        return this.productDetail.descInfo.imgs
      }
      return []
    },

    formatPrice(price) {
      if (!price || isNaN(price)) return '0.00'
      return (price / 100).toFixed(2)
    },

    formatTime(timestamp) {
      if (!timestamp) return '-'
      const date = new Date(timestamp * 1000)
      return date.toLocaleString('zh-CN')
    },

    getStatusType(status) {
      switch (status) {
        case 1:
          return 'success'
        case 2:
          return 'warning'
        case 3:
          return 'danger'
        default:
          return 'info'
      }
    },

    getStatusText(status) {
      switch (status) {
        case 1:
          return '正常'
        case 2:
          return '下架'
        case 3:
          return '售罄'
        default:
          return '未知'
      }
    },

    getCommissionType(type) {
      switch (type) {
        case 1:
          return '固定佣金'
        case 2:
          return '比例佣金'
        default:
          return '未知类型'
      }
    },

    getCouponStatusType(status) {
      switch (status) {
        case 1:
          return 'success'
        case 2:
          return 'warning'
        case 3:
          return 'danger'
        default:
          return 'info'
      }
    },

    getCouponStatusText(status) {
      switch (status) {
        case 1:
          return '有效'
        case 2:
          return '即将过期'
        case 3:
          return '已过期'
        default:
          return '未知'
      }
    },

    previewImage(imageUrl) {
      this.previewImageUrl = imageUrl
      this.imagePreviewVisible = true
    },

    handleImageError(event) {
      event.target.src = 'https://via.placeholder.com/200x200?text=Image+Error'
    },

    formatCommissionRatio(ratio) {
      if (!ratio || isNaN(ratio)) return '0.00'
      return (ratio / 10000).toFixed(2)
    },
  },
}
</script>

<style scoped>
.product-detail-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  margin: 0 0 0 15px;
  color: #303133;
  font-size: 24px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.detail-container {
  max-width: 1200px;
  margin: 0 auto;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.product-images h3 {
  margin-bottom: 15px;
  color: #303133;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.basic-info {
  padding-left: 20px;
}

.product-title {
  font-size: 24px;
  color: #303133;
  margin-bottom: 10px;
  line-height: 1.4;
}

.product-subtitle {
  font-size: 16px;
  color: #909399;
  margin-bottom: 20px;
  line-height: 1.5;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: bold;
  color: #606266;
  width: 100px;
  flex-shrink: 0;
}

.info-item .value {
  color: #303133;
}

.sku-attrs {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.attr-tag {
  margin: 2px;
}

.price {
  color: #e6a23c;
  font-weight: bold;
  font-size: 16px;
}

.category-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.category-level {
  font-size: 12px;
  color: #909399;
}

.commission-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.commission-item .label {
  font-weight: bold;
  color: #606266;
  width: 80px;
  flex-shrink: 0;
}

.commission-rate {
  color: #67c23a;
  font-weight: bold;
  font-size: 18px;
}

.coupon-amount {
  color: #f56c6c;
  font-weight: bold;
}

.desc-content {
  padding: 10px 0;
}

.desc-text h4,
.desc-images h4 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
}

.desc-text p {
  line-height: 1.6;
  color: #606266;
  margin-bottom: 20px;
}

.desc-image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.desc-image-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.desc-image-item img:hover {
  transform: scale(1.05);
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  background: white;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .product-detail-page {
    padding: 10px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .basic-info {
    padding-left: 0;
    margin-top: 20px;
  }

  .desc-image-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}
</style>
