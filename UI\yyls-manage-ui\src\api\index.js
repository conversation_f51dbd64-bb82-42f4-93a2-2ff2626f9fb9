import * as loginApi from '@/api/system/login'
import * as menuApi from '@/api/system/menu'
import * as roleApi from '@/api/system/role'
import * as sysUserApi from '@/api/system/user'
import * as authApi from '@/api/system/auth'
import * as redisApi from '@/api/system/redis'
import * as systemNoticeApi from '@/api/system/notice'

import * as fileApi from '@/api/file'
import * as optionsApi from '@/api/options'

import * as domainApi from '@/api/domain/domain'

import * as actionConfigApi from '@/api/action/actionconfig'

import * as platformApi from '@/api/platform/platform'
import * as platformCategoryApi from '@/api/platform/platformcategory'

import * as wxlProductApi from '@/api/product/wxlproduct'

export {
  loginApi,
  menuApi,
  roleApi,
  sysUserApi,
  authApi,
  redisApi,
  systemNoticeApi,
  fileApi,
  optionsApi,
  domainApi,
  actionConfigApi,
  platformApi,
  platformCategoryApi,
  wxlProductApi,
}
